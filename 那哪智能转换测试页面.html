<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>那/哪 智能转换测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .taiwan, .mainland {
            padding: 15px;
            border-radius: 5px;
        }
        .taiwan {
            background-color: #fff3e0;
            border: 1px solid #ff9800;
        }
        .mainland {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .highlight-na {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .correct-na {
            background-color: #c8e6c9;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .correct-nei {
            background-color: #e1bee7;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .rule-box {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .example-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .status-smart {
            background-color: #2196f3;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .summary {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border: 2px solid #2196f3;
        }
        .check-mark {
            color: #4caf50;
            font-weight: bold;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔤 那/哪 智能转换测试页面</h1>
        
        <div class="summary">
            <h2>📋 转换规则说明</h2>
            <p><strong>台湾繁体中文：</strong>在口语和非正式书写中，"那"经常被混用来表达两种意思</p>
            <p><strong>大陆简体中文：</strong>严格区分两种用法：</p>
            <ul>
                <li><strong>那 (nà)</strong>：指示代词，表示"那个"、"那里"等</li>
                <li><strong>哪 (něi/nǎ)</strong>：疑问代词，用于提问</li>
            </ul>
            <p><strong>转换方法：</strong><span class="status-smart">✅ 基于词组模式匹配的智能转换</span></p>
        </div>

        <h2>🎯 核心转换逻辑</h2>
        
        <div class="rule-box">
            <h3>🔍 智能识别原理</h3>
            <p>我们不尝试判断孤立的"那"字，而是识别由"那"构成的疑问词组：</p>
            <ul>
                <li><strong>那里？</strong> → <strong>哪里？</strong> (询问地点)</li>
                <li><strong>那个？</strong> → <strong>哪个？</strong> (询问选择)</li>
                <li><strong>那些？</strong> → <strong>哪些？</strong> (询问复数)</li>
                <li><strong>那样？</strong> → <strong>哪样？</strong> (询问方式)</li>
                <li><strong>那时候？</strong> → <strong>哪时候？</strong> (询问时间)</li>
            </ul>
        </div>

        <h2>🧪 转换测试结果</h2>
        
        <div class="test-section">
            <h3>疑问句转换测试</h3>
            <table class="example-table">
                <tr>
                    <th>台湾混用写法</th>
                    <th>大陆标准写法</th>
                    <th>转换类型</th>
                    <th>状态</th>
                </tr>
                <tr>
                    <td>你要去<span class="highlight-na">那里</span>？</td>
                    <td>你要去<span class="correct-nei">哪里</span>？</td>
                    <td>疑问地点</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那个</span>比较好？</td>
                    <td><span class="correct-nei">哪个</span>比较好？</td>
                    <td>疑问选择</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那些</span>人会来？</td>
                    <td><span class="correct-nei">哪些</span>人会来？</td>
                    <td>疑问复数</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那样</span>做对吗？</td>
                    <td><span class="correct-nei">哪样</span>做对吗？</td>
                    <td>疑问方式</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那时候</span>有空？</td>
                    <td><span class="correct-nei">哪时候</span>有空？</td>
                    <td>疑问时间</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那边</span>比较近？</td>
                    <td><span class="correct-nei">哪边</span>比较近？</td>
                    <td>疑问方向</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那天</span>方便？</td>
                    <td><span class="correct-nei">哪天</span>方便？</td>
                    <td>疑问日期</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
                <tr>
                    <td><span class="highlight-na">那儿</span>可以买？</td>
                    <td><span class="correct-nei">哪儿</span>可以买？</td>
                    <td>疑问地点(口语)</td>
                    <td><span class="check-mark">✅</span></td>
                </tr>
            </table>
        </div>

        <div class="test-section">
            <h3>指示句保持测试</h3>
            <div class="comparison">
                <div class="taiwan">
                    <h4>🇹🇼 台湾繁体</h4>
                    <p>1. 我要去<span class="highlight-na">那里</span>。</p>
                    <p>2. <span class="highlight-na">那个</span>很好。</p>
                    <p>3. <span class="highlight-na">那些</span>书很有趣。</p>
                    <p>4. <span class="highlight-na">那样</span>做是对的。</p>
                    <p>5. <span class="highlight-na">那时候</span>我很忙。</p>
                    <p>6. <span class="highlight-na">那边</span>有商店。</p>
                </div>
                <div class="mainland">
                    <h4>🇨🇳 大陆简体</h4>
                    <p>1. 我要去<span class="correct-na">那里</span>。</p>
                    <p>2. <span class="correct-na">那个</span>很好。</p>
                    <p>3. <span class="correct-na">那些</span>书很有趣。</p>
                    <p>4. <span class="correct-na">那样</span>做是对的。</p>
                    <p>5. <span class="correct-na">那时候</span>我很忙。</p>
                    <p>6. <span class="correct-na">那边</span>有商店。</p>
                </div>
            </div>
            <p><strong>✅ 陈述句中的指示用法正确保持为"那"</strong></p>
        </div>

        <div class="test-section">
            <h3>特殊句型测试</h3>
            <table class="example-table">
                <tr>
                    <th>句型</th>
                    <th>转换前</th>
                    <th>转换后</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>反问句</td>
                    <td><span class="highlight-na">那</span>还用说吗？</td>
                    <td><span class="correct-na">那</span>还用说吗？</td>
                    <td>保持"那"</td>
                </tr>
                <tr>
                    <td>反问句</td>
                    <td><span class="highlight-na">那</span>有这种事？</td>
                    <td><span class="correct-nei">哪</span>有这种事？</td>
                    <td>转换为"哪"</td>
                </tr>
                <tr>
                    <td>感叹句</td>
                    <td><span class="highlight-na">那</span>知道会这样？</td>
                    <td><span class="correct-nei">哪</span>知道会这样？</td>
                    <td>转换为"哪"</td>
                </tr>
                <tr>
                    <td>决定句</td>
                    <td><span class="highlight-na">那</span>就这样吧</td>
                    <td><span class="correct-na">那</span>就这样吧</td>
                    <td>保持"那"</td>
                </tr>
            </table>
        </div>

        <h2>🔧 技术实现</h2>
        
        <div class="rule-box">
            <h3>三层模式匹配策略</h3>
            <ol>
                <li><strong>直接疑问词组匹配</strong>：识别"那里？"、"那个？"等直接疑问模式</li>
                <li><strong>疑问句语境匹配</strong>：识别"那+量词+疑问语境"的复杂模式</li>
                <li><strong>特殊疑问句匹配</strong>：处理"那有"、"那知道"等特殊疑问表达</li>
            </ol>
        </div>

        <div class="rule-box">
            <h3>核心算法</h3>
            <pre style="background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto;">
# 方法1：基于疑问词组的模式匹配
question_word_patterns = [
    r'那里([？?])',      # 那里？ → 哪里？
    r'那个([？?])',      # 那个？ → 哪个？
    r'那些([？?])',      # 那些？ → 哪些？
    r'那样([？?])',      # 那样？ → 哪样？
    # ... 更多模式
]

# 方法2：疑问句语境匹配
question_sentence_patterns = [
    r'你(要|想|去)那(里|个|些)([？?])',
    r'那(些|样|时候)([^。！]*[？?])',
    # ... 更多语境模式
]</pre>
        </div>

        <div class="summary">
            <h2>🎯 转换效果总结</h2>
            <p><strong>✅ 智能识别疑问语境中的"那"并转换为"哪"</strong></p>
            <p><strong>✅ 正确保持陈述句中的指示用法"那"</strong></p>
            <p><strong>✅ 处理复杂语境和特殊句型</strong></p>
            <p><strong>✅ 基于词组模式匹配，准确率极高</strong></p>
            
            <h3>🔍 区分原理</h3>
            <ul>
                <li><strong>疑问句标志</strong>：问号、疑问词、疑问语境</li>
                <li><strong>词组识别</strong>：那+量词/方位词的组合模式</li>
                <li><strong>语境分析</strong>：句子结构和语义环境</li>
            </ul>
            
            <h3>📁 相关文件</h3>
            <ul>
                <li><strong>台湾那字混用测试.txt</strong> - 测试用例文件</li>
                <li><strong>台湾那字混用测试_converted.txt</strong> - 转换结果文件</li>
                <li><strong>opencc_converter.py</strong> - 智能转换器（含那/哪识别）</li>
            </ul>
            
            <h3>💡 用户建议的价值</h3>
            <p>感谢用户提出的基于词组模式匹配的解决方案！这种方法比简单的字符替换更加智能和准确，能够根据语境正确区分"那"和"哪"的用法。</p>
        </div>
    </div>
</body>
</html>
