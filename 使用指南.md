# OpenCC 批量文件转换器 - 使用指南

## 🎯 项目概述

已成功使用 uv 搭建了 Python 环境，并创建了一个功能完整的 OpenCC 批量文件转换器。该工具专门用于繁简中文转换，支持 Word 文档和文本文件的批量处理。

## ✅ 已完成的功能

### 1. 环境搭建
- ✅ 使用 uv 创建 Python 3.11.12 虚拟环境
- ✅ 安装必要依赖：opencc-python-reimplemented, python-docx
- ✅ 项目结构完整，包含配置文件和锁定文件

### 2. 核心转换器 (`opencc_converter.py`)
- ✅ 支持多种 OpenCC 配置 (tw2sp, s2t, t2s, s2tw 等)
- ✅ 智能文本编码检测 (UTF-8, GBK, GB2312)
- ✅ Word 文档格式保持 (段落、表格)
- ✅ 单文件和批量转换功能
- ✅ 命令行界面支持

### 3. 测试和演示
- ✅ 完整的功能测试套件 (`test_opencc.py`)
- ✅ 使用示例和演示 (`example_usage.py`)
- ✅ 多种配置测试验证
- ✅ 转换效果验证

## 🚀 快速开始

### 基本使用命令

```bash
# 1. 转换单个文本文件 (繁体→简体)
uv run python opencc_converter.py input.txt

# 2. 转换 Word 文档
uv run python opencc_converter.py document.docx

# 3. 批量转换目录
uv run python opencc_converter.py input_folder --batch

# 4. 指定输出目录
uv run python opencc_converter.py input.txt -o output_folder

# 5. 使用不同转换配置
uv run python opencc_converter.py input.txt -c s2t  # 简体→繁体
```

### 运行测试和演示

```bash
# 运行完整测试套件
uv run python test_opencc.py

# 运行使用演示
uv run python example_usage.py

# 查看环境状态
uv run python test_environment.py
```

## 📊 转换效果示例

### 繁体转简体 (tw2sp 配置)

**原文：**
```
軟體開發技術文檔
程式設計是一門藝術，需要邏輯思維和創造力。
資料庫管理系統提供了資料的儲存、檢索和管理功能。
```

**转换后：**
```
软件开发技术文档
编程是一门艺术，需要逻辑思维和创造力。
数据库管理系统提供了数据的存储、检索和管理功能。
```

## 📁 项目文件结构

```
d:\opencc\
├── opencc_converter.py      # 主转换器程序
├── example_usage.py         # 使用示例和演示
├── test_opencc.py          # 完整测试套件
├── test_environment.py     # 环境测试脚本
├── README_OpenCC.md        # 详细说明文档
├── 使用指南.md             # 本文件
├── pyproject.toml          # 项目配置
├── uv.lock                 # 依赖锁定文件
├── main.py                 # 初始示例文件
├── test_files/             # 测试文件目录
└── test_samples/           # 示例文件目录
```

## 🔧 支持的转换配置

| 配置 | 说明 | 推荐用途 |
|------|------|----------|
| tw2sp | 台湾繁体 → 简体 | **推荐**，最常用 |
| tw2s | 台湾繁体 → 简体 | 备选方案 |
| t2s | 繁体 → 简体 | 通用繁简转换 |
| s2t | 简体 → 繁体 | 简体转繁体 |
| s2tw | 简体 → 台湾繁体 | 转换为台湾用词 |
| s2twp | 简体 → 台湾繁体 | 包含短语转换 |

## 💡 使用建议

### 1. 文件处理建议
- **备份原文件**：转换前请备份重要文件
- **独立输出目录**：批量转换时指定独立的输出目录
- **文件命名**：转换后文件自动添加 `_converted` 后缀

### 2. 转换配置选择
- **台湾繁体文档**：推荐使用 `tw2sp` 配置
- **大陆繁体文档**：可使用 `t2s` 配置
- **简体转繁体**：推荐使用 `s2tw` 或 `s2twp`

### 3. 批量处理技巧
- 使用 `--batch` 参数进行目录批量转换
- 指定输出目录避免文件混乱
- 支持递归处理子目录

## 🔍 故障排除

### 常见问题解决

1. **OpenCC 初始化失败**
   ```bash
   # 重新安装 OpenCC
   uv add opencc-python-reimplemented --force-reinstall
   ```

2. **文件编码问题**
   - 程序自动检测多种编码格式
   - 支持 UTF-8, GBK, GB2312 等

3. **Word 文档转换失败**
   ```bash
   # 重新安装 python-docx
   uv add python-docx --force-reinstall
   ```

## 📈 性能特点

- ✅ **高效转换**：基于 OpenCC 高性能转换引擎
- ✅ **批量处理**：支持目录递归批量转换
- ✅ **格式保持**：Word 文档保持原有格式
- ✅ **编码智能**：自动检测和处理多种文本编码
- ✅ **错误处理**：完善的异常处理和错误报告

## 🎉 总结

OpenCC 批量文件转换器已经完全搭建完成并经过全面测试。主要特点：

1. **环境完整**：使用 uv 搭建的现代 Python 环境
2. **功能全面**：支持文本和 Word 文档的批量转换
3. **配置灵活**：支持多种 OpenCC 转换配置
4. **使用简单**：提供命令行和编程两种接口
5. **测试充分**：包含完整的测试套件和演示程序

现在您可以开始使用这个工具进行繁简中文文档的批量转换了！

---

**环境信息：**
- Python: 3.11.12
- uv: 0.8.14
- OpenCC: opencc-python-reimplemented 0.1.7
- 平台: Windows 10
