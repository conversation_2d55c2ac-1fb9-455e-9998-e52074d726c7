#!/usr/bin/env python3
"""
OpenCC 批量文件转换器
支持 Word (.docx) 和文本文件 (.txt) 的繁简转换
使用 tw2sp.json 配置文件进行繁体中文到简体中文的转换
"""

import os
import sys
from pathlib import Path
from typing import List, Optional
import argparse
import opencc
from docx import Document
from docx.shared import Inches

class OpenCCConverter:
    """OpenCC 文件转换器"""

    def __init__(self, config: str = 'tw2sp', enable_punctuation: bool = False):
        """
        初始化转换器

        Args:
            config: OpenCC 配置文件，默认使用 tw2sp (繁体转简体)
            enable_punctuation: 是否启用标点符号转换
        """
        try:
            # 直接使用配置名称，不添加 .json 后缀
            self.converter = opencc.OpenCC(config)
            self.config = config
            self.enable_punctuation = enable_punctuation

            # 标点符号映射表（只转换真正有使用差异的标点符号）
            self.punctuation_map = {
                '「': "'",  # 繁体一般引号 -> 简体单引号
                '」': "'",  # 繁体一般引号 -> 简体单引号
                '『': '"',  # 繁体强调引号 -> 简体双引号
                '』': '"',  # 繁体强调引号 -> 简体双引号
                # 注意：以下标点符号在繁简中文中使用相同，不进行转换：
                # '（': '(',  # 繁简都使用全角括号
                # '）': ')',  # 繁简都使用全角括号
                # '，': ',',  # 繁简都使用全角逗号
                # '。': '.',  # 繁简都使用全角句号
                # '：': ':',  # 繁简都使用全角冒号
                # '；': ';',  # 繁简都使用全角分号
                # '！': '!',  # 繁简都使用全角感叹号
                # '？': '?',  # 繁简都使用全角问号
                # '【': '[',  # 繁简都常用全角方括号
                # '】': ']',  # 繁简都常用全角方括号
                # '…': '...',  # 省略号形式各地有差异，但不强制转换
                # '——': '--',  # 繁简都使用相同的破折号
            }

            punctuation_status = "启用" if enable_punctuation else "禁用"
            print(f"✅ OpenCC 转换器初始化成功，使用配置: {config}，标点转换: {punctuation_status}")
        except Exception as e:
            print(f"❌ OpenCC 转换器初始化失败: {e}")
            print("可用的配置文件: tw2sp, tw2s, t2s, s2t, s2tw, s2twp, hk2s, s2hk, t2hk, t2tw")
            sys.exit(1)
    
    def convert_text(self, text: str) -> str:
        """转换文本"""
        try:
            # 首先进行汉字转换
            result = self.converter.convert(text)

            # 如果启用标点符号转换
            if self.enable_punctuation:
                # 判断是否为转简体的配置
                is_to_simplified = any(marker in self.config for marker in ['2s', '2sp'])

                if is_to_simplified:
                    # 繁体标点 -> 简体标点
                    for traditional, simplified in self.punctuation_map.items():
                        result = result.replace(traditional, simplified)

            # 修正括号内句号的位置（符合大陆标点规范 GB/T 15834—2011）
            if self.enable_punctuation:
                import re

                # 句内括号识别规则：
                # 1. 经文引用（包含数字、章节号）
                # 2. 注释性短语（如果、一种、传统等关键词）
                # 3. 补充说明性内容

                # 句内括号：经文引用 （书卷章节。） -> （书卷章节）
                result = re.sub(r'（([^）]*[0-9～\-]+[^）]*)。）', r'（\1）', result)

                # 句内括号：注释性短语
                sentence_internal_patterns = [
                    r'（(一种[^）]*)。）',      # 一种传统的说法
                    r'（(如果[^）]*)。）',      # 如果能买到票的话
                    r'（(参见[^）]*)。）',      # 参见某某书
                    r'（(見[^）]*)。）',        # 見某某书
                    r'（(传统[^）]*)。）',      # 传统的说法
                    r'（(據[^）]*)。）',        # 据说
                    r'（(根据[^）]*)。）',      # 根据某某
                    r'（(比如[^）]*)。）',      # 比如说
                    r'（(例如[^）]*)。）',      # 例如
                ]

                for pattern in sentence_internal_patterns:
                    result = re.sub(pattern, r'（\1）', result)

                # 句内括号：处理句末+注释的情况 那里。（约五39～40。） -> 那里（约五39～40）。
                result = re.sub(r'([^。])。（([^）]*[0-9～\-]+[^）]*)。）', r'\1（\2）。', result)

                # 处理句末+注释性短语的情况
                for pattern in sentence_internal_patterns:
                    # 将 那里。（如果...。） 转换为 那里（如果...）。
                    sentence_end_pattern = pattern.replace('（(', '([^。])。（(').replace('。）', '。）')
                    result = re.sub(sentence_end_pattern, r'\1（\2）。', result)

                for pattern in sentence_internal_patterns:
                    # 将 。（...。） 转换为 （...）。
                    modified_pattern = pattern.replace('（(', '。（(').replace('。）', '。）')
                    replacement = r'（\1）。'
                    result = re.sub(modified_pattern, replacement, result)

                # 注意：句外括号（独立完整句子）保持 。（...。） 格式不变
                # 这些通常包含完整的句子，有主语、谓语、宾语等

                # 处理"的"和"地"的区别（台湾繁体统一用"的"，大陆简体区分"的"和"地"）
                if any(marker in self.config for marker in ['2s', '2sp']):  # 转为简体时
                    result = self.convert_de_to_de_di(result)

            return result
        except Exception as e:
            print(f"❌ 文本转换失败: {e}")
            return text

    def convert_de_to_de_di(self, text: str) -> str:
        """
        智能转换"的/地/得"，支持多种词性标注库

        台湾繁体：统一用"的"
        大陆简体：
        - 的 + 名词 (定语)：美丽的风景
        - 地 + 动词 (状语)：慢慢地走
        - 得 + 形容词/副词 (补语)：跑得快
        """

        # 尝试多种词性标注库，按优先级排序
        methods = [
            self._convert_with_jieba_enhanced,  # 增强版 jieba
            self._convert_with_jieba_basic,     # 基础版 jieba
            self._convert_with_rules,           # 规则匹配
        ]

        for method in methods:
            try:
                result = method(text)
                if result != text:  # 如果有转换结果
                    return result
            except Exception as e:
                continue

        return text  # 如果所有方法都失败，返回原文

    def _convert_with_jieba_enhanced(self, text: str) -> str:
        """使用增强版 jieba 进行转换"""
        import jieba.posseg as pseg
        import jieba

        # 添加自定义词典以提高准确率
        custom_words = [
            ('慢慢', 'd'),    # 副词
            ('仔细', 'a'),    # 形容词
            ('认真', 'a'),    # 形容词
            ('激动', 'a'),    # 形容词
            ('努力', 'ad'),   # 副形词
            ('大声', 'd'),    # 副词
            ('小心', 'a'),    # 形容词
        ]

        for word, flag in custom_words:
            jieba.add_word(word, tag=flag)

        # 分词并标注词性
        words = list(pseg.cut(text))

        result_parts = []
        i = 0

        while i < len(words):
            word, flag = words[i].word, words[i].flag

            # 查找"的"字
            if word == '的' and i > 0 and i < len(words) - 1:
                prev_word, prev_flag = words[i-1].word, words[i-1].flag
                next_word, next_flag = words[i+1].word, words[i+1].flag

                # 使用增强的判断规则
                converted_de = self.decide_de_di_de_enhanced(prev_word, prev_flag, next_word, next_flag)
                result_parts.append(converted_de)
            else:
                result_parts.append(word)

            i += 1

        return ''.join(result_parts)

    def _convert_with_jieba_basic(self, text: str) -> str:
        """使用基础版 jieba 进行转换"""
        import jieba.posseg as pseg

        words = list(pseg.cut(text))
        result_parts = []
        i = 0

        while i < len(words):
            word, flag = words[i].word, words[i].flag

            if word == '的' and i > 0 and i < len(words) - 1:
                prev_word, prev_flag = words[i-1].word, words[i-1].flag
                next_word, next_flag = words[i+1].word, words[i+1].flag

                converted_de = self.decide_de_di_de(prev_word, prev_flag, next_word, next_flag)
                result_parts.append(converted_de)
            else:
                result_parts.append(word)

            i += 1

        return ''.join(result_parts)

    def _convert_with_rules(self, text: str) -> str:
        """使用规则匹配进行转换（备用方案）"""
        import re

        result = text

        # 高置信度的状语模式
        high_confidence_patterns = [
            r'(慢慢|仔细|认真|激动|努力|大声|小心|突然|立刻)的([走跑跳站坐看听说想做学写读])',
            r'(慢慢|静静|悄悄|轻轻)的([走跑跳爬游飞])',
            r'(仔细|认真|专心|用心)的([看听想学做写读])',
            r'(激动|兴奋|高兴|开心)的([站跳笑哭叫喊])',
        ]

        for pattern in high_confidence_patterns:
            result = re.sub(pattern, r'\1地\2', result)

        # 高置信度的补语模式
        complement_patterns = [
            r'([走跑跳飞游爬])的(很快|非常快|特别快)',
            r'([做写画唱说])的(很好|非常好|特别好)',
            r'([学读背记])的(很快|很好|很认真)',
        ]

        for pattern in complement_patterns:
            result = re.sub(pattern, r'\1得\2', result)

        return result

    def decide_de_di_de_enhanced(self, prev_word: str, prev_flag: str, next_word: str, next_flag: str) -> str:
        """
        增强版的"的/地/得"判断规则
        结合词性标注和语义规则
        """

        # 优先使用语义规则（高置信度）
        semantic_result = self._apply_semantic_rules(prev_word, next_word)
        if semantic_result:
            return semantic_result

        # 然后使用词性规则
        return self.decide_de_di_de(prev_word, prev_flag, next_word, next_flag)

    def _apply_semantic_rules(self, prev_word: str, next_word: str) -> str:
        """应用语义规则"""

        # 高置信度的状语词汇
        definite_adverbials = {
            '慢慢', '快快', '静静', '悄悄', '轻轻', '突然', '忽然', '立刻', '马上',
            '仔细', '认真', '小心', '用心', '专心', '激动', '兴奋', '高兴', '努力',
            '大声', '小声', '轻声', '默默', '渐渐', '逐渐'
        }

        # 高置信度的动词
        definite_verbs = {
            '走', '跑', '跳', '站', '坐', '躺', '看', '听', '说', '讲', '想', '做', '学', '写', '读',
            '走了', '跑了', '站起来', '坐下来', '看着', '听着', '说着', '想着', '做着'
        }

        # 高置信度的名词
        definite_nouns = {
            '风景', '花朵', '树木', '房子', '汽车', '书籍', '电脑', '手机', '衣服', '鞋子',
            '朋友', '老师', '学生', '孩子', '父母', '工作', '学习', '生活', '时间', '地方'
        }

        # 高置信度的形容词/副词（用于补语）
        definite_complements = {
            '很快', '很好', '很美', '很高', '很大', '很小', '非常好', '特别快', '十分美丽'
        }

        # 规则1：状语 + 地 + 动词
        if prev_word in definite_adverbials and next_word in definite_verbs:
            return '地'

        # 规则2：动词 + 得 + 补语
        if prev_word in definite_verbs and next_word in definite_complements:
            return '得'

        # 规则3：形容词 + 的 + 名词
        if next_word in definite_nouns:
            return '的'

        return None  # 无法确定，交给词性规则处理

    def decide_de_di_de(self, prev_word: str, prev_flag: str, next_word: str, next_flag: str) -> str:
        """
        根据词性决定使用"的"、"地"还是"得"

        Args:
            prev_word: 前一个词
            prev_flag: 前一个词的词性
            next_word: 后一个词
            next_flag: 后一个词的词性

        Returns:
            str: "的"、"地"或"得"
        """

        # 规则1: 地 + 动词 (状语)
        # 前面是副词(ad)、形容词(a)等，后面是动词(v)
        if self.is_adverbial(prev_flag) and self.is_verb(next_flag):
            return '地'

        # 规则2: 得 + 形容词/副词 (补语)
        # 前面是动词(v)，后面是形容词(a)、副词(ad)
        if self.is_verb(prev_flag) and self.is_adjective_or_adverb(next_flag):
            return '得'

        # 规则3: 的 + 名词 (定语)
        # 前面是形容词(a)、名词(n)等，后面是名词(n)
        if self.is_attributive(prev_flag) and self.is_noun(next_flag):
            return '的'

        # 特殊情况处理
        # 一些常见的状语词汇，即使词性标注不准确也要转换
        adverbial_words = {
            '慢慢', '快快', '静静', '悄悄', '轻轻', '突然', '忽然', '立刻', '马上',
            '激动', '兴奋', '高兴', '仔细', '认真', '小心', '努力', '大声', '小声'
        }

        if prev_word in adverbial_words and self.is_verb(next_flag):
            return '地'

        # 默认返回"的"
        return '的'

    def is_adverbial(self, flag: str) -> bool:
        """判断是否为状语词性（副词、形容词等）"""
        return flag in ['ad', 'a', 'd']  # ad=副词, a=形容词, d=副词

    def is_verb(self, flag: str) -> bool:
        """判断是否为动词"""
        return flag.startswith('v')  # v=动词, vn=动名词, vi=不及物动词等

    def is_adjective_or_adverb(self, flag: str) -> bool:
        """判断是否为形容词或副词"""
        return flag in ['a', 'ad', 'd']

    def is_noun(self, flag: str) -> bool:
        """判断是否为名词"""
        return flag.startswith('n')  # n=名词, nr=人名, ns=地名等

    def is_attributive(self, flag: str) -> bool:
        """判断是否可作定语（形容词、名词等）"""
        return flag.startswith('a') or flag.startswith('n') or flag in ['r']  # r=代词
    
    def convert_txt_file(self, input_path: Path, output_path: Path) -> bool:
        """
        转换 TXT 文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 尝试不同的编码格式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            content = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(input_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    used_encoding = encoding
                    break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                print(f"❌ 无法读取文件 {input_path}，尝试了多种编码格式")
                return False
            
            print(f"📖 读取文件 {input_path} (编码: {used_encoding})")
            
            # 转换内容
            converted_content = self.convert_text(content)
            
            # 写入输出文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(converted_content)
            
            print(f"✅ TXT 文件转换完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ TXT 文件转换失败 {input_path}: {e}")
            return False
    
    def convert_docx_file(self, input_path: Path, output_path: Path) -> bool:
        """
        转换 DOCX 文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            print(f"📖 读取 Word 文档 {input_path}")
            
            # 读取 Word 文档
            doc = Document(input_path)
            
            # 转换段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    original_text = paragraph.text
                    converted_text = self.convert_text(original_text)
                    
                    # 清空段落并添加转换后的文本
                    paragraph.clear()
                    paragraph.add_run(converted_text)
            
            # 转换表格中的文本
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                original_text = paragraph.text
                                converted_text = self.convert_text(original_text)
                                
                                paragraph.clear()
                                paragraph.add_run(converted_text)
            
            # 保存转换后的文档
            doc.save(output_path)
            print(f"✅ Word 文档转换完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ Word 文档转换失败 {input_path}: {e}")
            return False
    
    def convert_file(self, input_path: Path, output_dir: Optional[Path] = None) -> bool:
        """
        转换单个文件
        
        Args:
            input_path: 输入文件路径
            output_dir: 输出目录，如果为 None 则在原文件目录创建
            
        Returns:
            bool: 转换是否成功
        """
        if not input_path.exists():
            print(f"❌ 文件不存在: {input_path}")
            return False
        
        # 确定输出路径
        if output_dir is None:
            output_dir = input_path.parent
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成输出文件名
        stem = input_path.stem
        suffix = input_path.suffix
        output_filename = f"{stem}_converted{suffix}"
        output_path = output_dir / output_filename
        
        # 根据文件类型选择转换方法
        if suffix.lower() == '.txt':
            return self.convert_txt_file(input_path, output_path)
        elif suffix.lower() == '.docx':
            return self.convert_docx_file(input_path, output_path)
        else:
            print(f"❌ 不支持的文件类型: {suffix}")
            return False
    
    def batch_convert(self, input_dir: Path, output_dir: Optional[Path] = None, 
                     file_extensions: List[str] = None) -> dict:
        """
        批量转换目录中的文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            file_extensions: 支持的文件扩展名列表
            
        Returns:
            dict: 转换结果统计
        """
        if file_extensions is None:
            file_extensions = ['.txt', '.docx']
        
        if not input_dir.exists():
            print(f"❌ 输入目录不存在: {input_dir}")
            return {'success': 0, 'failed': 0, 'skipped': 0}
        
        if output_dir is None:
            output_dir = input_dir / 'converted'
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        results = {'success': 0, 'failed': 0, 'skipped': 0}
        
        print(f"🔍 扫描目录: {input_dir}")
        print(f"📁 输出目录: {output_dir}")
        print(f"📋 支持的文件类型: {', '.join(file_extensions)}")
        print("-" * 60)
        
        # 遍历目录中的文件
        for file_path in input_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in file_extensions:
                print(f"🔄 处理文件: {file_path.name}")
                
                # 保持相对目录结构
                relative_path = file_path.relative_to(input_dir)
                output_file_dir = output_dir / relative_path.parent
                
                if self.convert_file(file_path, output_file_dir):
                    results['success'] += 1
                else:
                    results['failed'] += 1
            elif file_path.is_file():
                results['skipped'] += 1
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OpenCC 批量文件转换器')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出目录路径')
    parser.add_argument('-c', '--config', default='tw2sp',
                       help='OpenCC 配置文件 (默认: tw2sp)')
    parser.add_argument('--batch', action='store_true', 
                       help='批量处理模式（处理目录中的所有支持文件）')
    
    args = parser.parse_args()
    
    # 初始化转换器
    converter = OpenCCConverter(args.config)
    
    input_path = Path(args.input)
    output_path = Path(args.output) if args.output else None
    
    print("=" * 60)
    print("🔄 OpenCC 文件转换器")
    print("=" * 60)
    
    if args.batch or input_path.is_dir():
        # 批量转换模式
        results = converter.batch_convert(input_path, output_path)
        
        print("=" * 60)
        print("📊 转换结果统计:")
        print(f"  ✅ 成功: {results['success']} 个文件")
        print(f"  ❌ 失败: {results['failed']} 个文件")
        print(f"  ⏭️  跳过: {results['skipped']} 个文件")
        print("=" * 60)
    else:
        # 单文件转换模式
        success = converter.convert_file(input_path, output_path)
        if success:
            print("🎉 文件转换完成！")
        else:
            print("❌ 文件转换失败！")
            sys.exit(1)

if __name__ == "__main__":
    main()
