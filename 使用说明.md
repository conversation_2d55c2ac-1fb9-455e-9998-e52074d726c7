# OpenCC 批量文件转换器 - 使用说明

## 🚀 快速开始

### 启动 GUI 界面
```bash
# 方法1：双击批处理文件
启动GUI.bat

# 方法2：命令行启动
uv run python opencc_gui_simple.py
```

### 命令行使用
```bash
# 单文件转换
uv run python opencc_converter.py input.txt

# 批量转换
uv run python opencc_converter.py input_folder --batch

# 指定输出目录
uv run python opencc_converter.py input.txt -o output_folder
```

## 🎯 主要功能

### ✅ 已实现功能
- **繁简转换**：使用 OpenCC tw2sp 配置
- **标点符号转换**：「」『』→ ""，符合大陆标点规范
- **批量处理**：支持文件和目录批量转换
- **文件格式**：支持 .txt 和 .docx 文件
- **图形界面**：简洁易用的 GUI 界面
- **标点规范化**：自动修正括号内句号位置

### 🔧 转换配置
- **tw2sp**：台湾繁体 → 简体中文（推荐）
- **s2t**：简体中文 → 繁体中文
- **其他配置**：t2s, s2tw, s2twp 等

## 📱 GUI 界面使用

### 界面布局
```
🔧 转换配置
├─ 转换类型: [tw2sp ▼] 台湾繁体 → 简体中文
└─ ☑ 转换标点符号 (推荐启用)

📁 文件选择
├─ [📄 选择文件] [📂 选择目录] [🗑️ 清空列表]
└─ 文件列表显示区域

📤 输出设置
└─ 输出目录: [浏览]

🎮 控制区域
├─ [🚀 开始转换] [⏹️ 停止]
├─ 进度条
└─ 状态显示

📋 转换日志
└─ 实时日志显示
```

### 使用步骤
1. **选择转换配置**：推荐使用 tw2sp，启用标点转换
2. **添加文件**：选择要转换的文件或目录
3. **设置输出**：可选择输出目录（默认在原文件目录）
4. **开始转换**：点击开始按钮，查看进度和日志

## 📊 转换效果

### 标点符号转换
```
原文: 「這是測試」，包含標點符號。
转换: "这是测试"，包含标点符号。
```

### 括号规范化
```
原文: 然而他們卻不肯到主耶穌那裡。（約五39～40。）
转换: 然而他们却不肯到主耶稣那里（约五39～40）。
```

## 📁 项目文件

### 核心文件
- `opencc_converter.py` - 主转换器（支持标点转换）
- `opencc_gui_simple.py` - GUI 界面
- `启动GUI.bat` - GUI 启动脚本

### 配置文件
- `pyproject.toml` - 项目配置
- `uv.lock` - 依赖锁定

### 说明文档
- `使用说明.md` - 本文件
- `标点符号转换说明.md` - 详细的标点转换说明

## 🔧 高级功能

### 编程接口
```python
from opencc_converter import OpenCCConverter

# 启用标点符号转换
converter = OpenCCConverter('tw2sp', enable_punctuation=True)

# 转换文本
result = converter.convert_text('「這是測試」')
print(result)  # 输出: "这是测试"

# 转换文件
converter.convert_file(Path('input.txt'))
```

### 批量转换
```python
# 批量转换目录
results = converter.batch_convert(
    input_dir=Path('input_folder'),
    output_dir=Path('output_folder')
)
print(f"成功: {results['success']}, 失败: {results['failed']}")
```

## ⚠️ 注意事项

1. **备份文件**：转换前请备份重要文件
2. **标点转换**：建议启用标点转换以符合大陆规范
3. **文件编码**：自动检测 UTF-8、GBK、GB2312 编码
4. **Word 文档**：保持原有格式和表格结构

## 🎉 特色功能

- ✅ **解决了 tw2sp.json 不转换标点的问题**
- ✅ **符合中国大陆标点符号使用规范**
- ✅ **智能括号句号位置修正**
- ✅ **保持宗教文献的庄重感**
- ✅ **图形界面简洁易用**

---

**环境要求：** Python 3.11+ + uv  
**支持平台：** Windows 10+  
**最后更新：** 2025-08-29
