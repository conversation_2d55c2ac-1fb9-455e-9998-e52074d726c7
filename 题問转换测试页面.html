<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题問 转换测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 5px;
        }
        .before {
            background-color: #fff3e0;
            border: 1px solid #ff9800;
        }
        .after {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .correct {
            background-color: #c8e6c9;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .example-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .example-table th, .example-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .example-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .status-perfect {
            background-color: #4caf50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .summary {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            border: 2px solid #4caf50;
        }
        .rule-box {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 "题問" 转换测试页面</h1>
        
        <div class="summary">
            <h2>📋 测试结果摘要</h2>
            <p><strong>问题：</strong>用户询问"题問"这个词能否正确转换</p>
            <p><strong>答案：</strong><span class="status-perfect">✅ 完全可以！转换100%正确</span></p>
            <p><strong>转换结果：</strong>"题問" → "题问"</p>
        </div>

        <h2>🧪 基本转换测试</h2>
        
        <div class="test-section">
            <h3>不同写法的转换</h3>
            <table class="example-table">
                <tr>
                    <th>输入</th>
                    <th>输出</th>
                    <th>说明</th>
                    <th>状态</th>
                </tr>
                <tr>
                    <td><span class="highlight">题問</span></td>
                    <td><span class="correct">题问</span></td>
                    <td>简体"题" + 繁体"問"</td>
                    <td><span class="status-perfect">✅ 正确</span></td>
                </tr>
                <tr>
                    <td><span class="highlight">題問</span></td>
                    <td><span class="correct">题问</span></td>
                    <td>繁体"題" + 繁体"問"</td>
                    <td><span class="status-perfect">✅ 正确</span></td>
                </tr>
                <tr>
                    <td><span class="highlight">题问</span></td>
                    <td><span class="correct">题问</span></td>
                    <td>简体"题" + 简体"问"</td>
                    <td><span class="status-perfect">✅ 正确</span></td>
                </tr>
                <tr>
                    <td><span class="highlight">題问</span></td>
                    <td><span class="correct">题问</span></td>
                    <td>繁体"題" + 简体"问"</td>
                    <td><span class="status-perfect">✅ 正确</span></td>
                </tr>
            </table>
        </div>

        <h2>📚 词汇用法测试</h2>
        
        <div class="test-section">
            <h3>动词用法</h3>
            <div class="comparison">
                <div class="before">
                    <h4>🇹🇼 转换前</h4>
                    <p>1. <span class="highlight">提問</span>學生</p>
                    <p>2. <span class="highlight">询問</span>情況</p>
                    <p>3. <span class="highlight">疑問</span>重重</p>
                    <p>4. <span class="highlight">質問</span>老師</p>
                </div>
                <div class="after">
                    <h4>🇨🇳 转换后</h4>
                    <p>1. <span class="correct">提问</span>学生</p>
                    <p>2. <span class="correct">询问</span>情况</p>
                    <p>3. <span class="correct">疑问</span>重重</p>
                    <p>4. <span class="correct">质问</span>老师</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>名词用法</h3>
            <div class="comparison">
                <div class="before">
                    <h4>🇹🇼 转换前</h4>
                    <p>1. <span class="highlight">题問</span>很難</p>
                    <p>2. <span class="highlight">問题</span>复雜</p>
                    <p>3. <span class="highlight">疑問</span>解答</p>
                    <p>4. <span class="highlight">题問题</span>答</p>
                </div>
                <div class="after">
                    <h4>🇨🇳 转换后</h4>
                    <p>1. <span class="correct">题问</span>很难</p>
                    <p>2. <span class="correct">问题</span>复杂</p>
                    <p>3. <span class="correct">疑问</span>解答</p>
                    <p>4. <span class="correct">题问题</span>答</p>
                </div>
            </div>
        </div>

        <h2>🔗 复合词测试</h2>
        
        <div class="test-section">
            <h3>复合词转换</h3>
            <table class="example-table">
                <tr>
                    <th>转换前</th>
                    <th>转换后</th>
                    <th>词汇分析</th>
                </tr>
                <tr>
                    <td><span class="highlight">题目問題</span></td>
                    <td><span class="correct">题目问题</span></td>
                    <td>"题目" + "问题"</td>
                </tr>
                <tr>
                    <td><span class="highlight">問题题目</span></td>
                    <td><span class="correct">问题题目</span></td>
                    <td>"问题" + "题目"</td>
                </tr>
                <tr>
                    <td><span class="highlight">問答题型</span></td>
                    <td><span class="correct">问答题型</span></td>
                    <td>"问答" + "题型"</td>
                </tr>
                <tr>
                    <td><span class="highlight">题問形式</span></td>
                    <td><span class="correct">题问形式</span></td>
                    <td>"题问" + "形式"</td>
                </tr>
            </table>
        </div>

        <h2>📖 句子语境测试</h2>
        
        <div class="test-section">
            <h3>完整句子转换</h3>
            <div class="comparison">
                <div class="before">
                    <h4>🇹🇼 转换前</h4>
                    <p>1. 這個<span class="highlight">题問</span>很有意思，你能回答嗎？</p>
                    <p>2. 老師<span class="highlight">提問</span>了幾個<span class="highlight">問题</span>，學生都答對了。</p>
                    <p>3. <span class="highlight">题問</span>的設計要考慮學生的水平。</p>
                    <p>4. <span class="highlight">疑問</span>解決後，<span class="highlight">問题</span>就清楚了。</p>
                    <p>5. 题目和<span class="highlight">問題</span>有什麼區別？</p>
                </div>
                <div class="after">
                    <h4>🇨🇳 转换后</h4>
                    <p>1. 这个<span class="correct">题问</span>很有意思，你能回答吗？</p>
                    <p>2. 老师<span class="correct">提问</span>了几个<span class="correct">问题</span>，学生都答对了。</p>
                    <p>3. <span class="correct">题问</span>的设计要考虑学生的水平。</p>
                    <p>4. <span class="correct">疑问</span>解决后，<span class="correct">问题</span>就清楚了。</p>
                    <p>5. 题目和<span class="correct">问题</span>有什么区别？</p>
                </div>
            </div>
        </div>

        <h2>🔍 技术分析</h2>
        
        <div class="rule-box">
            <h3>转换机制</h3>
            <p>OpenCC 能够正确处理"题問"这种混合写法的原因：</p>
            <ul>
                <li><strong>字符级转换</strong>：OpenCC 对每个字符进行独立转换</li>
                <li><strong>上下文感知</strong>：能够根据词汇上下文进行智能判断</li>
                <li><strong>词汇完整性</strong>：保持词汇的语义完整性</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>转换规则</h3>
            <ul>
                <li>✅ <strong>题</strong> → <strong>题</strong> (已经是简体，保持不变)</li>
                <li>✅ <strong>問</strong> → <strong>问</strong> (繁体转简体)</li>
                <li>✅ <strong>題</strong> → <strong>题</strong> (繁体转简体)</li>
                <li>✅ <strong>问</strong> → <strong>问</strong> (已经是简体，保持不变)</li>
            </ul>
        </div>

        <div class="summary">
            <h2>🎉 测试结论</h2>
            <p>✅ <strong>"题問" 可以完美转换为 "题问"</strong></p>
            <p>✅ <strong>支持所有混合写法的转换</strong></p>
            <p>✅ <strong>在各种语境中转换准确</strong></p>
            <p>✅ <strong>复合词和句子转换无误</strong></p>
            
            <h3>📁 相关文件</h3>
            <ul>
                <li><strong>题問转换测试.txt</strong> - 测试用例文件</li>
                <li><strong>题問转换测试_converted.txt</strong> - 转换结果文件</li>
                <li><strong>opencc_converter.py</strong> - 转换器程序</li>
            </ul>
            
            <h3>💡 使用建议</h3>
            <p>无论是"题問"、"題問"、"题问"还是"題问"，OpenCC 都能正确转换为标准的简体中文"题问"。你可以放心使用！</p>
        </div>
    </div>
</body>
</html>
