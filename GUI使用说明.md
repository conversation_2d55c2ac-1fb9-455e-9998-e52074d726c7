# OpenCC 批量文件转换器 - GUI 使用说明

## 🎯 GUI 界面介绍

我已经为您创建了两个版本的图形用户界面：

1. **opencc_gui_simple.py** - 简化版 GUI（推荐）
2. **opencc_gui.py** - 完整版 GUI（支持拖拽功能）

## 🚀 启动方式

### 方法 1：双击批处理文件
```
双击 "启动GUI.bat" 文件
```

### 方法 2：命令行启动
```bash
# 启动简化版 GUI
uv run python opencc_gui_simple.py

# 启动完整版 GUI（如果支持拖拽）
uv run python opencc_gui.py
```

## 📱 界面功能说明

### 1. 转换配置区域 🔧
- **转换类型下拉框**：选择转换配置
  - `tw2sp`：台湾繁体 → 简体中文（推荐）
  - `s2t`：简体中文 → 繁体中文
  - `t2s`：繁体中文 → 简体中文
  - 等其他配置选项
- **配置说明**：显示当前选择的转换方向

### 2. 文件选择区域 📁
- **📄 选择文件**：选择单个或多个文件进行转换
- **📂 选择目录**：选择整个目录进行批量转换
- **🗑️ 清空列表**：清空当前选择的文件列表
- **文件列表**：显示已选择的文件和目录
- **计数显示**：显示已选择的项目数量

### 3. 输出设置区域 📤
- **输出目录**：指定转换后文件的保存位置
- **浏览按钮**：选择输出目录
- **提示**：如果留空，将在原文件目录创建转换文件

### 4. 控制区域 🎮
- **🚀 开始转换**：启动转换任务
- **⏹️ 停止**：停止当前转换任务
- **进度条**：显示转换进度
- **状态显示**：显示当前操作状态

### 5. 转换日志区域 📋
- **实时日志**：显示转换过程的详细信息
- **时间戳**：每条日志都有时间标记
- **状态图标**：
  - ✅ 成功操作
  - ❌ 失败操作
  - 📄 文件操作
  - 📂 目录操作
  - 🚀 开始任务
  - 🎉 完成任务

## 📖 使用步骤

### 基本使用流程

1. **选择转换配置**
   - 在"转换配置"区域选择合适的转换类型
   - 推荐使用 `tw2sp` 进行繁体转简体

2. **添加文件**
   - 点击"📄 选择文件"添加单个文件
   - 点击"📂 选择目录"添加整个目录
   - 支持的文件类型：`.txt` 和 `.docx`

3. **设置输出目录**（可选）
   - 点击"浏览"选择输出目录
   - 如果不设置，转换文件将保存在原文件目录

4. **开始转换**
   - 点击"🚀 开始转换"按钮
   - 观察进度条和日志信息
   - 等待转换完成

### 高级功能

#### 批量目录转换
- 选择包含多个文件的目录
- 程序会递归处理所有支持的文件
- 保持原有的目录结构

#### 混合选择
- 可以同时选择文件和目录
- 程序会智能处理不同类型的输入

## 🎨 界面特色

### 视觉设计
- 🎯 清晰的功能分区
- 📊 实时进度显示
- 🎨 友好的图标和颜色
- 📱 响应式布局设计

### 用户体验
- ✅ 操作简单直观
- 📝 详细的操作日志
- ⚡ 实时状态反馈
- 🔄 支持批量处理

## 📊 转换示例

### 界面操作示例

1. **选择配置**：选择 "tw2sp"
2. **添加文件**：选择包含繁体中文的 `.txt` 或 `.docx` 文件
3. **开始转换**：点击开始按钮
4. **查看结果**：在日志中查看转换状态

### 转换效果预览
```
原文件：技术文档.txt
内容：軟體開發技術文檔

转换后：技术文档_converted.txt  
内容：软件开发技术文档
```

## ⚠️ 注意事项

### 文件处理
- **备份重要文件**：转换前请备份原始文件
- **文件命名**：转换后的文件会自动添加 `_converted` 后缀
- **编码支持**：自动检测 UTF-8、GBK、GB2312 等编码

### 性能优化
- **大文件处理**：大文件转换可能需要较长时间
- **批量处理**：目录中文件较多时请耐心等待
- **内存使用**：处理大量文件时注意内存使用情况

### 错误处理
- **文件权限**：确保有读写权限
- **文件格式**：确保 Word 文档格式正确
- **路径长度**：避免过长的文件路径

## 🔧 故障排除

### 常见问题

1. **GUI 无法启动**
   ```bash
   # 检查 Python 环境
   uv run python --version
   
   # 重新安装依赖
   uv sync
   ```

2. **转换失败**
   - 检查文件是否被其他程序占用
   - 确认文件格式是否支持
   - 查看日志中的错误信息

3. **界面卡顿**
   - 大批量转换时属于正常现象
   - 可以通过日志查看进度
   - 避免在转换过程中操作其他功能

## 🎉 功能亮点

### 已实现的功能
- ✅ 图形化界面操作
- ✅ 多种转换配置支持
- ✅ 批量文件处理
- ✅ 实时进度显示
- ✅ 详细日志记录
- ✅ 错误处理和提示
- ✅ 文件格式保持（Word 文档）

### 技术特点
- 🔄 多线程处理，界面不卡顿
- 📊 实时进度反馈
- 🛡️ 完善的错误处理
- 💾 智能编码检测
- 🎨 现代化界面设计

---

**开发信息：**
- GUI 框架：tkinter（Python 标准库）
- 转换引擎：OpenCC
- 支持平台：Windows 10+
- Python 版本：3.11+

现在您可以通过图形界面轻松进行繁简中文文档的批量转换了！
