# OpenCC 标点符号转换功能说明

## 🎯 问题解答

您提到的问题是正确的：**tw2sp.json 配置默认不支持标点符号转换**。

OpenCC 的标准配置主要专注于汉字的繁简转换，而不处理标点符号。

## ✅ 解决方案

我已经为您增强了 OpenCC 转换器，现在支持标点符号转换！

### 新增功能

1. **增强版转换器**：在原有 `OpenCCConverter` 基础上添加了标点符号转换功能
2. **可选启用**：通过 `enable_punctuation` 参数控制是否转换标点符号
3. **GUI 支持**：图形界面中添加了"转换标点符号"选项

## 📊 转换效果对比

### 标准 tw2sp.json（不转换标点）
```
原文: 「這是測試」，包含標點符號。
转换: 「这是测试」，包含标点符号。
```

### 增强版（转换标点符号）
```
原文: 「這是測試」，包含標點符號。
转换: "这是测试"，包含标点符号。
```

## 🔧 支持的标点符号转换

| 繁体标点 | 简体标点 | 说明 |
|---------|---------|------|
| 「 | " | 左引号 |
| 」 | " | 右引号 |
| 『 | ' | 左单引号 |
| 』 | ' | 右单引号 |
| （ | ( | 左括号 |
| ） | ) | 右括号 |
| 【 | [ | 左方括号 |
| 】 | ] | 右方括号 |
| ， | , | 逗号 |
| 。 | . | 句号 |
| ： | : | 冒号 |
| ； | ; | 分号 |
| ！ | ! | 感叹号 |
| ？ | ? | 问号 |
| … | ... | 省略号 |
| —— | -- | 破折号 |

## 🚀 使用方法

### 1. 命令行使用

```python
from opencc_converter import OpenCCConverter

# 不启用标点转换（默认）
converter1 = OpenCCConverter('tw2sp', enable_punctuation=False)
result1 = converter1.convert_text('「這是測試」')
print(result1)  # 输出: 「这是测试」

# 启用标点转换
converter2 = OpenCCConverter('tw2sp', enable_punctuation=True)
result2 = converter2.convert_text('「這是測試」')
print(result2)  # 输出: "这是测试"
```

### 2. GUI 界面使用

1. 启动 GUI：`uv run python opencc_gui_simple.py`
2. 在"转换配置"区域勾选"转换标点符号"选项
3. 选择文件进行转换

### 3. 批量文件转换

```bash
# 使用增强版转换器进行批量转换
# 转换器会根据 enable_punctuation 设置自动处理标点符号
```

## 🧪 测试验证

### 运行测试脚本
```bash
# 简单测试
uv run python simple_punct_test.py

# 完整测试
uv run python test_punctuation_conversion.py
```

### 测试结果示例
```
不启用标点转换: 「这是测试」
启用标点转换: "这是测试"
✅ 标点符号转换功能正常工作！
```

## 📁 文件转换示例

### 创建测试文件
```python
# 创建包含繁体标点的测试文件
test_content = '''「軟體開發」是一門藝術，需要『邏輯思維』。
重要提示：
1. 問題：為什麼？
2. 答案：因為（原因）。
3. 參考《書籍》…'''

# 保存为 test.txt
with open('test.txt', 'w', encoding='utf-8') as f:
    f.write(test_content)
```

### 转换文件
```python
from opencc_converter import OpenCCConverter

# 启用标点转换
converter = OpenCCConverter('tw2sp', enable_punctuation=True)
converter.convert_file(Path('test.txt'))

# 生成 test_converted.txt，标点符号已转换
```

## 🎨 GUI 界面更新

### 新增功能
- ✅ 在"转换配置"区域添加了"转换标点符号"复选框
- ✅ 默认启用标点符号转换
- ✅ 实时显示转换器状态（包含标点转换状态）
- ✅ 日志中显示标点转换设置变化

### 界面截图说明
```
🔧 转换配置
┌─────────────────────────────────────────┐
│ 转换类型: [tw2sp ▼] 台湾繁体 → 简体中文  │
│ ☑ 转换标点符号                          │
└─────────────────────────────────────────┘
```

## 💡 使用建议

### 何时启用标点符号转换
- ✅ **推荐启用**：处理台湾繁体文档时
- ✅ **推荐启用**：需要统一标点符号格式时
- ✅ **推荐启用**：转换后用于大陆简体环境时

### 何时禁用标点符号转换
- ❌ **保持原样**：需要保持原始标点符号时
- ❌ **特殊需求**：有特定标点符号要求时

## 🔍 技术实现

### 实现原理
1. **汉字转换**：使用标准 OpenCC 进行汉字转换
2. **标点转换**：使用字符串替换进行标点符号转换
3. **智能处理**：根据转换方向自动选择标点符号映射

### 代码结构
```python
class OpenCCConverter:
    def __init__(self, config, enable_punctuation=False):
        self.converter = opencc.OpenCC(config)
        self.enable_punctuation = enable_punctuation
        self.punctuation_map = {...}  # 标点符号映射表
    
    def convert_text(self, text):
        # 1. 汉字转换
        result = self.converter.convert(text)
        
        # 2. 标点符号转换（如果启用）
        if self.enable_punctuation:
            for traditional, simplified in self.punctuation_map.items():
                result = result.replace(traditional, simplified)
        
        return result
```

## 🎉 总结

现在您的 OpenCC 转换器已经支持标点符号转换了！

### 主要改进
1. ✅ **解决了 tw2sp.json 不转换标点的问题**
2. ✅ **提供了可选的标点符号转换功能**
3. ✅ **GUI 界面支持标点转换选项**
4. ✅ **保持了原有的所有功能**

### 使用方式
- **命令行**：`OpenCCConverter('tw2sp', enable_punctuation=True)`
- **GUI界面**：勾选"转换标点符号"选项
- **批量转换**：自动应用标点转换设置

现在您可以根据需要选择是否转换标点符号，完美解决了 tw2sp.json 的限制！
