#!/usr/bin/env python3
"""
OpenCC 批量文件转换器 - 简化版 GUI
不依赖拖拽功能，使用标准 tkinter
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import os
from pathlib import Path
from opencc_converter import OpenCCConverter

class SimpleOpenCCGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("OpenCC 批量文件转换器")
        self.root.geometry("700x550")
        self.root.minsize(600, 400)
        
        # 初始化变量
        self.converter = None
        self.current_config = tk.StringVar(value="tw2sp")
        self.enable_punctuation = tk.BooleanVar(value=True)  # 默认启用标点转换
        self.input_paths = []
        self.output_dir = tk.StringVar()
        self.progress_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        
        # 初始化转换器
        self.init_converter()
        
        # 启动进度更新线程
        self.root.after(100, self.check_progress_queue)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔄 OpenCC 批量文件转换器", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 转换配置区域
        config_frame = ttk.LabelFrame(main_frame, text="🔧 转换配置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(2, weight=1)
        
        ttk.Label(config_frame, text="转换类型:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        config_combo = ttk.Combobox(config_frame, textvariable=self.current_config, 
                                   values=['tw2sp', 'tw2s', 't2s', 's2t', 's2tw', 's2twp'],
                                   state='readonly', width=12)
        config_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 15))
        config_combo.bind('<<ComboboxSelected>>', self.on_config_changed)
        
        # 配置说明
        config_descriptions = {
            'tw2sp': '台湾繁体 → 简体中文 (推荐)',
            'tw2s': '台湾繁体 → 简体中文',
            't2s': '繁体中文 → 简体中文',
            's2t': '简体中文 → 繁体中文',
            's2tw': '简体中文 → 台湾繁体',
            's2twp': '简体中文 → 台湾繁体 (含短语)'
        }
        
        self.config_desc_label = ttk.Label(config_frame, text=config_descriptions['tw2sp'],
                                          foreground='blue')
        self.config_desc_label.grid(row=0, column=2, sticky=tk.W, padx=(10, 0))

        # 标点符号转换选项
        punct_check = ttk.Checkbutton(config_frame, text="转换标点符号",
                                     variable=self.enable_punctuation,
                                     command=self.on_punctuation_changed)
        punct_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="📁 文件选择", padding="10")
        file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        file_frame.columnconfigure(0, weight=1)
        file_frame.rowconfigure(2, weight=1)
        
        # 文件选择按钮
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="📄 选择文件", command=self.select_files).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="📂 选择目录", command=self.select_directory).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🗑️ 清空列表", command=self.clear_files).pack(side=tk.LEFT, padx=(0, 20))
        
        # 统计信息
        self.file_count_label = ttk.Label(button_frame, text="已选择: 0 项", foreground='gray')
        self.file_count_label.pack(side=tk.RIGHT)
        
        # 文件列表标题
        ttk.Label(file_frame, text="选中的文件和目录:", font=('Arial', 9, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        # 文件列表框架
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 文件列表
        self.file_listbox = tk.Listbox(list_frame, height=6, font=('Consolas', 9))
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 输出设置区域
        output_frame = ttk.LabelFrame(main_frame, text="📤 输出设置", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="输出目录:", font=('Arial', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        output_entry = ttk.Entry(output_frame, textvariable=self.output_dir, font=('Consolas', 9))
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(output_frame, text="浏览", command=self.select_output_dir).grid(row=0, column=2)
        
        ttk.Label(output_frame, text="💡 留空则在原文件目录创建转换文件", 
                 foreground='gray', font=('Arial', 8)).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # 控制按钮和进度区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        control_frame.columnconfigure(1, weight=1)
        
        # 控制按钮
        button_control_frame = ttk.Frame(control_frame)
        button_control_frame.grid(row=0, column=0, sticky=tk.W)
        
        self.convert_button = ttk.Button(button_control_frame, text="🚀 开始转换", 
                                        command=self.start_conversion, style='Accent.TButton')
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_control_frame, text="⏹️ 停止", 
                                     command=self.stop_conversion, state='disabled')
        self.stop_button.pack(side=tk.LEFT)
        
        # 状态和进度
        status_frame = ttk.Frame(control_frame)
        status_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(20, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪", font=('Arial', 9))
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="📋 转换日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD, 
                                                 font=('Consolas', 9))
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        main_frame.rowconfigure(2, weight=2)
        main_frame.rowconfigure(5, weight=1)
    
    def on_config_changed(self, event=None):
        """转换配置改变时的处理"""
        config_descriptions = {
            'tw2sp': '台湾繁体 → 简体中文 (推荐)',
            'tw2s': '台湾繁体 → 简体中文',
            't2s': '繁体中文 → 简体中文',
            's2t': '简体中文 → 繁体中文',
            's2tw': '简体中文 → 台湾繁体',
            's2twp': '简体中文 → 台湾繁体 (含短语)'
        }
        
        self.config_desc_label.config(text=config_descriptions.get(self.current_config.get(), ''))
        self.init_converter()

    def on_punctuation_changed(self):
        """标点符号转换选项改变时的处理"""
        self.init_converter()
        punct_status = "启用" if self.enable_punctuation.get() else "禁用"
        self.log_message(f"🔧 标点符号转换: {punct_status}")
    
    def init_converter(self):
        """初始化转换器"""
        try:
            self.converter = OpenCCConverter(self.current_config.get(),
                                           enable_punctuation=self.enable_punctuation.get())
            punct_status = "启用" if self.enable_punctuation.get() else "禁用"
            self.log_message(f"✅ 转换器初始化成功，配置: {self.current_config.get()}，标点转换: {punct_status}")
        except Exception as e:
            self.log_message(f"❌ 转换器初始化失败: {e}")
            messagebox.showerror("错误", f"转换器初始化失败: {e}")
    
    def select_files(self):
        """选择文件"""
        files = filedialog.askopenfilenames(
            title="选择要转换的文件",
            filetypes=[
                ("支持的文件", "*.txt *.docx"),
                ("文本文件", "*.txt"),
                ("Word 文档", "*.docx"),
                ("所有文件", "*.*")
            ]
        )
        
        added_count = 0
        for file_path in files:
            if file_path not in self.input_paths:
                self.input_paths.append(file_path)
                self.file_listbox.insert(tk.END, f"📄 {os.path.basename(file_path)}")
                added_count += 1
        
        if added_count > 0:
            self.update_file_count()
            self.log_message(f"📄 添加了 {added_count} 个文件")
    
    def select_directory(self):
        """选择目录"""
        directory = filedialog.askdirectory(title="选择要转换的目录")
        
        if directory and directory not in self.input_paths:
            self.input_paths.append(directory)
            self.file_listbox.insert(tk.END, f"📂 {os.path.basename(directory)} (目录)")
            self.update_file_count()
            self.log_message(f"📂 添加了目录: {os.path.basename(directory)}")
    
    def select_output_dir(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)
            self.log_message(f"📤 设置输出目录: {directory}")
    
    def clear_files(self):
        """清空文件列表"""
        self.input_paths.clear()
        self.file_listbox.delete(0, tk.END)
        self.update_file_count()
        self.log_message("🗑️ 已清空文件列表")
    
    def update_file_count(self):
        """更新文件计数显示"""
        count = len(self.input_paths)
        self.file_count_label.config(text=f"已选择: {count} 项")
    
    def log_message(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 更新状态标签
        clean_message = message.replace("✅ ", "").replace("❌ ", "").replace("📄 ", "").replace("📂 ", "")
        self.status_label.config(text=clean_message)
    
    def start_conversion(self):
        """开始转换"""
        if not self.input_paths:
            messagebox.showwarning("警告", "请先选择要转换的文件或目录")
            return
        
        if not self.converter:
            messagebox.showerror("错误", "转换器未初始化")
            return
        
        # 禁用开始按钮，启用停止按钮
        self.convert_button.config(state='disabled')
        self.stop_button.config(state='normal')
        
        # 重置进度条
        self.progress_var.set(0)
        
        # 清空之前的日志
        self.log_text.delete(1.0, tk.END)
        self.log_message("🚀 开始转换任务...")
        
        # 在新线程中执行转换
        self.conversion_thread = threading.Thread(target=self.run_conversion, daemon=True)
        self.conversion_thread.start()
    
    def run_conversion(self):
        """在后台线程中运行转换"""
        try:
            total_items = len(self.input_paths)
            success_count = 0
            failed_count = 0
            
            output_dir = Path(self.output_dir.get()) if self.output_dir.get() else None
            
            for i, input_path in enumerate(self.input_paths):
                path_obj = Path(input_path)
                
                # 更新进度
                progress = (i / total_items) * 100
                self.progress_queue.put(('progress', progress))
                self.progress_queue.put(('status', f"处理: {path_obj.name}"))
                
                try:
                    if path_obj.is_file():
                        # 单文件转换
                        success = self.converter.convert_file(path_obj, output_dir)
                        if success:
                            success_count += 1
                            self.progress_queue.put(('log', f"✅ 文件转换成功: {path_obj.name}"))
                        else:
                            failed_count += 1
                            self.progress_queue.put(('log', f"❌ 文件转换失败: {path_obj.name}"))
                    
                    elif path_obj.is_dir():
                        # 目录批量转换
                        self.progress_queue.put(('log', f"📂 开始处理目录: {path_obj.name}"))
                        results = self.converter.batch_convert(path_obj, output_dir)
                        success_count += results['success']
                        failed_count += results['failed']
                        
                        self.progress_queue.put(('log', 
                            f"📂 目录处理完成: {path_obj.name} (成功: {results['success']}, 失败: {results['failed']})"))
                
                except Exception as e:
                    failed_count += 1
                    self.progress_queue.put(('log', f"❌ 处理失败 {path_obj.name}: {e}"))
            
            # 转换完成
            self.progress_queue.put(('progress', 100))
            self.progress_queue.put(('status', f"转换完成！成功: {success_count}, 失败: {failed_count}"))
            self.progress_queue.put(('log', f"🎉 所有转换任务完成！成功: {success_count}, 失败: {failed_count}"))
            
        except Exception as e:
            self.progress_queue.put(('log', f"❌ 转换过程出错: {e}"))
        
        finally:
            self.progress_queue.put(('finished', None))
    
    def stop_conversion(self):
        """停止转换"""
        self.convert_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.log_message("⏹️ 用户停止了转换")
    
    def check_progress_queue(self):
        """检查进度队列并更新UI"""
        try:
            while True:
                msg_type, data = self.progress_queue.get_nowait()
                
                if msg_type == 'progress':
                    self.progress_var.set(data)
                elif msg_type == 'status':
                    self.status_label.config(text=data)
                elif msg_type == 'log':
                    self.log_message(data)
                elif msg_type == 'finished':
                    self.convert_button.config(state='normal')
                    self.stop_button.config(state='disabled')
                    break
                
        except queue.Empty:
            pass
        
        # 继续检查队列
        self.root.after(100, self.check_progress_queue)

def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleOpenCCGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()
