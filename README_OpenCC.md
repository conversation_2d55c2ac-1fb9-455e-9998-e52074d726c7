# OpenCC 批量文件转换器

这是一个基于 OpenCC 的批量文件转换工具，支持繁简中文转换，可以处理 Word (.docx) 和文本文件 (.txt)。

## 🚀 功能特性

- ✅ 支持 Word 文档 (.docx) 和文本文件 (.txt) 转换
- ✅ 使用 OpenCC tw2sp.json 配置进行繁体转简体转换
- ✅ 支持单文件转换和批量目录转换
- ✅ 自动检测文本编码 (UTF-8, GBK, GB2312)
- ✅ 保持 Word 文档格式和表格结构
- ✅ 命令行界面，易于使用

## 📦 环境要求

- Python 3.11+
- uv 包管理器
- 依赖包：
  - opencc-python-reimplemented
  - python-docx

## 🛠️ 安装和设置

1. 确保已安装 uv：
```bash
pip install uv
```

2. 安装项目依赖：
```bash
uv add opencc-python-reimplemented python-docx
```

## 📖 使用方法

### 命令行使用

#### 1. 单文件转换
```bash
# 转换文本文件
uv run python opencc_converter.py input.txt

# 转换 Word 文档
uv run python opencc_converter.py document.docx

# 指定输出目录
uv run python opencc_converter.py input.txt -o output_folder
```

#### 2. 批量转换
```bash
# 批量转换目录中的所有支持文件
uv run python opencc_converter.py input_folder --batch

# 批量转换并指定输出目录
uv run python opencc_converter.py input_folder --batch -o output_folder
```

#### 3. 使用不同的转换配置
```bash
# 简体转繁体
uv run python opencc_converter.py input.txt -c s2t

# 其他可用配置
uv run python opencc_converter.py input.txt -c t2s    # 繁体转简体
uv run python opencc_converter.py input.txt -c s2tw   # 简体转台湾繁体
uv run python opencc_converter.py input.txt -c tw2s   # 台湾繁体转简体
```

### 编程接口使用

```python
from opencc_converter import OpenCCConverter
from pathlib import Path

# 创建转换器实例
converter = OpenCCConverter('tw2sp')  # 繁体转简体

# 转换文本
text = "繁體中文轉換測試"
converted = converter.convert_text(text)
print(converted)  # 输出: 繁体中文转换测试

# 转换单个文件
input_file = Path("input.txt")
converter.convert_file(input_file)

# 批量转换目录
input_dir = Path("input_folder")
output_dir = Path("output_folder")
results = converter.batch_convert(input_dir, output_dir)
print(f"成功转换: {results['success']} 个文件")
```

## 🔧 支持的配置文件

| 配置名称 | 说明 |
|---------|------|
| tw2sp | 台湾繁体 → 简体中文 (推荐) |
| tw2s | 台湾繁体 → 简体中文 |
| t2s | 繁体中文 → 简体中文 |
| s2t | 简体中文 → 繁体中文 |
| s2tw | 简体中文 → 台湾繁体 |
| s2twp | 简体中文 → 台湾繁体 (包含短语) |
| hk2s | 香港繁体 → 简体中文 |
| s2hk | 简体中文 → 香港繁体 |
| t2hk | 繁体中文 → 香港繁体 |
| t2tw | 繁体中文 → 台湾繁体 |

## 📁 输出文件命名

- 单文件转换：`原文件名_converted.扩展名`
- 批量转换：在指定输出目录中保持原有目录结构

## 🎯 使用示例

### 示例 1：转换繁体中文文本文件
```bash
uv run python opencc_converter.py traditional_text.txt
```
输出：`traditional_text_converted.txt`

### 示例 2：批量转换文档目录
```bash
uv run python opencc_converter.py documents/ --batch -o converted_docs/
```

### 示例 3：运行演示程序
```bash
uv run python example_usage.py
```

## 🔍 转换效果示例

**原文 (繁体)：**
```
繁體中文轉換測試
臺灣正體字
軟體開發與資料庫管理
網際網路技術
電腦程式設計
```

**转换后 (简体)：**
```
繁体中文转换测试
台湾正体字
软件开发与数据库管理
互联网技术
电脑编程
```

## ⚠️ 注意事项

1. **文件编码**：程序会自动尝试多种编码格式 (UTF-8, GBK, GB2312)
2. **Word 文档**：支持段落文本和表格内容转换，保持原有格式
3. **批量转换**：避免重复转换已转换的文件
4. **备份建议**：转换前建议备份原文件

## 🐛 故障排除

### 常见问题

1. **OpenCC 初始化失败**
   - 确保已正确安装 `opencc-python-reimplemented`
   - 检查配置文件名称是否正确

2. **文件读取失败**
   - 检查文件路径是否正确
   - 确保文件不是损坏的

3. **Word 文档转换失败**
   - 确保 Word 文档不是受保护的
   - 检查是否安装了 `python-docx`

## 📞 技术支持

如果遇到问题，请检查：
1. Python 版本是否为 3.11+
2. 所有依赖包是否正确安装
3. 输入文件是否可读
4. 输出目录是否有写入权限

---

**开发环境：** Python 3.11.12 + uv 0.8.14  
**测试平台：** Windows 10  
**最后更新：** 2025-08-29
